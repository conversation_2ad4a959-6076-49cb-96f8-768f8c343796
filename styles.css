/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Performance optimizations */
.guide-card,
.btn,
.card-btn {
    transform: translateZ(0); /* Enable hardware acceleration */
    backface-visibility: hidden; /* Prevent flickering */
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background-color: #0a0a0a;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 2px solid #00ff88;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-family: 'Orbitron', monospace;
    font-weight: 900;
    font-size: 1.8rem;
    color: #ffffff;
}

.highlight {
    color: #00ff88;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #00ff88;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #00ff88;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%2300ff88" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-content {
    text-align: center;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffffff, #00ff88);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 20px rgba(0, 255, 136, 0.5); }
    to { text-shadow: 0 0 30px rgba(0, 255, 136, 0.8); }
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    color: #cccccc;
    font-weight: 300;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.15s ease,
                background-color 0.15s ease,
                color 0.15s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    color: #000000;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 255, 136, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #00ff88;
    border: 2px solid #00ff88;
}

.btn-secondary:hover {
    background: #00ff88;
    color: #000000;
    transform: translateY(-2px);
}

/* Main Content */
.main-content {
    margin-top: 0;
}

.guide-section {
    padding: 80px 0;
    position: relative;
}

.guide-section.dark {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #00ff88;
}

.section-description {
    font-size: 1.2rem;
    color: #cccccc;
    max-width: 600px;
    margin: 0 auto;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.guide-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    border: 1px solid rgba(0, 255, 136, 0.2);
    transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                border-color 0.15s ease,
                box-shadow 0.15s ease;
    backdrop-filter: blur(10px);
    will-change: transform;
}

.guide-card:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: #00ff88;
    box-shadow: 0 20px 40px rgba(0, 255, 136, 0.15);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.guide-card h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #00ff88;
}

.guide-card p {
    color: #cccccc;
    margin-bottom: 25px;
    line-height: 1.6;
}

.card-btn {
    background: transparent;
    color: #00ff88;
    border: 2px solid #00ff88;
    padding: 10px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: background-color 0.15s ease,
                color 0.15s ease,
                transform 0.15s ease;
    font-weight: 500;
}

.card-btn:hover {
    background: #00ff88;
    color: #000000;
    transform: translateY(-1px);
}

/* Footer */
.footer {
    background: #000000;
    padding: 50px 0 20px;
    border-top: 2px solid #00ff88;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3,
.footer-section h4 {
    color: #00ff88;
    margin-bottom: 15px;
    font-family: 'Orbitron', monospace;
}

.footer-section p {
    color: #cccccc;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #00ff88;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #333;
    color: #888;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #00ff88;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #00cc6a;
}
